import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import mangaService from '../../services/manga-service';
import { MangaManagementResponse } from '../../interfaces/models/manga';
import { Pagination } from '../../components/common/Pagination';
import { SearchBar } from '../../components/common/SearchBar';
import { Button } from '../../components/ui/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faEdit, faTrash, faEye, faBook } from '@fortawesome/free-solid-svg-icons';

const TranslatorMyMangas = () => {
  const navigate = useNavigate();
  const [mangas, setMangas] = useState<MangaManagementResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch mangas created by current user
  const fetchMyMangas = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const response = await mangaService.getMyMangas(page - 1, 10, search);
      if (response) {
        setMangas(response.content);
        setTotalPages(response.totalPages);
        setCurrentPage(response.number + 1);
      }
    } catch (error) {
      console.error('Error fetching my mangas:', error);
      toast.error('Không thể tải danh sách truyện của bạn', { position: 'top-right' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMyMangas(currentPage, searchTerm);
  }, [currentPage]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
    fetchMyMangas(1, term);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCreateManga = () => {
    navigate('/translator/my-mangas/create');
  };

  const handleEditManga = (id: string) => {
    navigate(`/translator/my-mangas/edit/${id}`);
  };

  const handleViewManga = (id: string) => {
    // Mở manga trong tab mới để xem
    window.open(`/manga/${id}`, '_blank');
  };

  const handleDeleteManga = async (id: string) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa truyện này?')) {
      try {
        await mangaService.deleteManga(id);
        toast.success('Xóa truyện thành công', { position: 'top-right' });
        fetchMyMangas(currentPage, searchTerm);
      } catch (error) {
        console.error('Error deleting manga:', error);
        toast.error('Không thể xóa truyện', { position: 'top-right' });
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Truyện của tôi</h1>
        <Button onClick={handleCreateManga} className="bg-green-600 hover:bg-green-700">
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Thêm truyện mới
        </Button>
      </div>

      <div className="mb-6">
        <SearchBar onSearch={handleSearch} placeholder="Tìm kiếm truyện..." />
      </div>

      {loading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <p className="mt-2 text-gray-600">Đang tải...</p>
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tiêu đề
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tác giả
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Trạng thái
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Lượt xem
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {mangas.length > 0 ? (
                    mangas.map((manga) => (
                      <tr key={manga.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{manga.title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{manga.author}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            manga.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                            manga.status === 'ONGOING' ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {manga.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {manga.views?.toLocaleString() || 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleViewManga(manga.id)}
                              className="text-blue-600 hover:text-blue-900 p-2 rounded-md hover:bg-blue-50"
                              title="Xem truyện"
                            >
                              <FontAwesomeIcon icon={faEye} />
                            </button>
                            <button
                              onClick={() => handleEditManga(manga.id)}
                              className="text-yellow-600 hover:text-yellow-900 p-2 rounded-md hover:bg-yellow-50"
                              title="Chỉnh sửa"
                            >
                              <FontAwesomeIcon icon={faEdit} />
                            </button>
                            <button
                              onClick={() => handleDeleteManga(manga.id)}
                              className="text-red-600 hover:text-red-900 p-2 rounded-md hover:bg-red-50"
                              title="Xóa"
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-12 text-center">
                        <div className="text-gray-500">
                          <FontAwesomeIcon icon={faBook} className="text-4xl mb-4" />
                          <p className="text-lg">Bạn chưa có truyện nào</p>
                          <p className="text-sm">Hãy tạo truyện đầu tiên của bạn!</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {totalPages > 1 && (
            <div className="mt-6 flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default TranslatorMyMangas;
