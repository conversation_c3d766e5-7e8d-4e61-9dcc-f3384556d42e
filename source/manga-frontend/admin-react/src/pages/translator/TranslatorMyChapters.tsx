import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import chapterService from '../../services/chapter-service';
import { ChapterResponse } from '../../interfaces/models/manga';
import { Pagination } from '../../components/common/Pagination';
import { SearchBar } from '../../components/common/SearchBar';
import { Button } from '../../components/ui/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faEdit, faTrash, faEye, faBookOpen } from '@fortawesome/free-solid-svg-icons';

const TranslatorMyChapters = () => {
  const navigate = useNavigate();
  const [chapters, setChapters] = useState<ChapterResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch chapters created by current user
  const fetchMyChapters = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const response = await chapterService.getMyChapters(page - 1, 10, search);
      if (response) {
        setChapters(response.content);
        setTotalPages(response.totalPages);
        setCurrentPage(response.number + 1);
      }
    } catch (error) {
      console.error('Error fetching my chapters:', error);
      toast.error('Không thể tải danh sách chương của bạn', { position: 'top-right' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMyChapters(currentPage, searchTerm);
  }, [currentPage]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
    fetchMyChapters(1, term);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCreateChapter = () => {
    navigate('/translator/my-chapters/create');
  };

  const handleEditChapter = (id: string) => {
    navigate(`/translator/my-chapters/edit/${id}`);
  };

  const handleViewChapter = (mangaId: string, chapterNumber: number) => {
    // Mở chapter trong tab mới để xem
    window.open(`/manga/${mangaId}/chapter/${chapterNumber}`, '_blank');
  };

  const handleDeleteChapter = async (id: string) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa chương này?')) {
      try {
        await chapterService.deleteChapter(id);
        toast.success('Xóa chương thành công', { position: 'top-right' });
        fetchMyChapters(currentPage, searchTerm);
      } catch (error) {
        console.error('Error deleting chapter:', error);
        toast.error('Không thể xóa chương', { position: 'top-right' });
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Chương của tôi</h1>
        <Button onClick={handleCreateChapter} className="bg-green-600 hover:bg-green-700">
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Thêm chương mới
        </Button>
      </div>

      <div className="mb-6">
        <SearchBar onSearch={handleSearch} placeholder="Tìm kiếm chương..." />
      </div>

      {loading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          <p className="mt-2 text-gray-600">Đang tải...</p>
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tên chương
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Truyện
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Số chương
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Lượt xem
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Thao tác
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {chapters.length > 0 ? (
                    chapters.map((chapter) => (
                      <tr key={chapter.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{chapter.title}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{chapter.mangaTitle}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">Chương {chapter.chapterNumber}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {chapter.views?.toLocaleString() || 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleViewChapter(chapter.mangaId, chapter.chapterNumber)}
                              className="text-blue-600 hover:text-blue-900 p-2 rounded-md hover:bg-blue-50"
                              title="Xem chương"
                            >
                              <FontAwesomeIcon icon={faEye} />
                            </button>
                            <button
                              onClick={() => handleEditChapter(chapter.id)}
                              className="text-yellow-600 hover:text-yellow-900 p-2 rounded-md hover:bg-yellow-50"
                              title="Chỉnh sửa"
                            >
                              <FontAwesomeIcon icon={faEdit} />
                            </button>
                            <button
                              onClick={() => handleDeleteChapter(chapter.id)}
                              className="text-red-600 hover:text-red-900 p-2 rounded-md hover:bg-red-50"
                              title="Xóa"
                            >
                              <FontAwesomeIcon icon={faTrash} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-12 text-center">
                        <div className="text-gray-500">
                          <FontAwesomeIcon icon={faBookOpen} className="text-4xl mb-4" />
                          <p className="text-lg">Bạn chưa có chương nào</p>
                          <p className="text-sm">Hãy tạo chương đầu tiên của bạn!</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {totalPages > 1 && (
            <div className="mt-6 flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default TranslatorMyChapters;
