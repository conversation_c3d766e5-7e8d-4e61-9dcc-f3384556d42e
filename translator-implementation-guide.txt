# HƯỚNG DẪN TRIỂN KHAI CHỨC NĂNG TRANSLATOR

## 1. CẬP NHẬT IDENTITY SERVICE

### Thêm Permission mới
Thêm permission TRANSLATOR_MANAGEMENT vào bảng permissions:
- name: "TRANSLATOR_MANAGEMENT"
- description: "Quyền dịch và quản lý truyện của riêng mình"

### Thêm Role mới
Thêm role TRANSLATOR vào bảng roles:
- name: "TRANSLATOR"
- description: "Dịch giả"
- permissions: [TRANSLATOR_MANAGEMENT]

### Cập nhật ApplicationInitConfig.java
```java
@Bean
@Transactional
ApplicationRunner applicationRunner(UserRepository userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository) {
    return args -> {
        // Thêm permission mới cho translator
        if (!permissionRepository.existsByName("TRANSLATOR_MANAGEMENT")) {
            permissionRepository.save(Permission.builder().name("TRANSLATOR_MANAGEMENT")
                    .description("Quyền dịch và quản lý truyện của riêng mình")
                    .build());
        }
        
        // Thêm role mới cho translator
        if (!roleRepository.existsByName("TRANSLATOR")) {
            roleRepository.save(Role.builder().name("TRANSLATOR")
                    .permissions(Set.of(permissionRepository.findByName("TRANSLATOR_MANAGEMENT")))
                    .description("Dịch giả")
                    .build());
        }
        
        // Phần còn lại giữ nguyên
    };
}
```

## 2. CẬP NHẬT MANGA SERVICE

### Cập nhật Entity Manga.java
Thêm trường createdBy để lưu ID người tạo:
```java
@Column(name = "created_by", updatable = false)
String createdBy;
```

### Cập nhật Entity Chapter.java
Thêm trường createdBy để lưu ID người tạo:
```java
@Column(name = "created_by", updatable = false)
String createdBy;
```

### Cập nhật MangaRepository.java
Thêm các phương thức tìm kiếm theo người tạo:
```java
Page<Manga> findByCreatedByAndDeletedFalse(String createdBy, Pageable pageable);

@Query("SELECT m FROM Manga m WHERE m.deleted = false AND m.createdBy = :createdBy " +
       "AND (:keyword IS NULL OR LOWER(m.title) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
       "OR LOWER(m.author) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
       "AND (:genreName IS NULL OR EXISTS (SELECT g FROM m.genres g WHERE LOWER(g.name) = LOWER(:genreName))) " +
       "AND (:status IS NULL OR m.status = :status) " +
       "AND (:yearOfRelease IS NULL OR m.yearOfRelease = :yearOfRelease)")
Page<Manga> searchAndFilterByCreatedBy(
        @Param("keyword") String keyword,
        @Param("genreName") String genreName,
        @Param("status") MangaStatus status,
        @Param("yearOfRelease") Integer yearOfRelease,
        @Param("createdBy") String createdBy,
        Pageable pageable);
```

### Cập nhật ChapterRepository.java
Thêm các phương thức tìm kiếm theo người tạo:
```java
List<Chapter> findByCreatedBy(String createdBy);

@Query("SELECT c FROM Chapter c WHERE c.manga.id = :mangaId AND c.createdBy = :createdBy")
List<Chapter> findByMangaIdAndCreatedBy(@Param("mangaId") String mangaId, @Param("createdBy") String createdBy);
```

### Cập nhật MangaService.java
Thêm phương thức kiểm tra quyền sở hữu và lọc truyện theo người tạo:
```java
// Thêm phương thức kiểm tra quyền sở hữu
public void checkOwnership(String mangaId, String userId) {
    Manga manga = mangaRepository.findById(mangaId)
            .orElseThrow(() -> new AppException(ErrorCode.MANGA_NOT_FOUND));
    
    if (!manga.getCreatedBy().equals(userId)) {
        throw new AppException(ErrorCode.UNAUTHORIZED_OPERATION);
    }
}

// Thêm phương thức lọc truyện theo người tạo
public Page<MangaManagementResponse> searchAndFilterMangasByCreatedBy(
        String keyword, String genreName, String statusStr, Integer yearOfRelease, 
        String createdBy, Pageable pageable) {
    
    MangaStatus status = null;
    if (statusStr != null && !statusStr.isEmpty()) {
        try {
            status = MangaStatus.valueOf(statusStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            // Bỏ qua lỗi, status vẫn là null
        }
    }
    
    Page<Manga> mangasPage = mangaRepository.searchAndFilterByCreatedBy(
            keyword, genreName, status, yearOfRelease, createdBy, pageable);
    
    List<String> mangaIds = mangasPage.getContent().stream()
            .map(Manga::getId).collect(Collectors.toList());
    Map<String, Integer> chapterCountsMap = getChapterCountsMap(mangaIds);
    
    return mangasPage.map(manga -> enrichManagementResponse(manga, chapterCountsMap));
}

// Cập nhật phương thức createManga để lưu người tạo
@Transactional
public MangaResponse createManga(MangaRequest request) {
    // Kiểm tra xem manga đã tồn tại chưa
    Manga existingManga = mangaRepository.findByTitle(request.getTitle());
    if (existingManga != null) {
        throw new AppException(ErrorCode.MANGA_ALREADY_EXISTS);
    }
    
    // Lấy thông tin người dùng hiện tại
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    String currentUserId = authentication.getName();
    
    var manga = mangaMapper.toManga(request);
    
    // Thiết lập người tạo
    manga.setCreatedBy(currentUserId);
    
    // Các xử lý khác giữ nguyên
    // ...
    
    return mangaMapper.toMangaResponse(manga);
}

// Cập nhật phương thức updateManga để kiểm tra quyền sở hữu
@Transactional
public MangaResponse updateManga(String id, MangaRequest request) {
    var manga = mangaRepository.findById(id)
            .orElseThrow(() -> new AppException(ErrorCode.MANGA_NOT_FOUND));
    
    // Kiểm tra quyền sở hữu
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    String currentUserId = authentication.getName();
    Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
    
    boolean hasTranslatorManagement = authorities.stream()
            .anyMatch(a -> a.getAuthority().equals("TRANSLATOR_MANAGEMENT"));
    boolean hasMangaManagement = authorities.stream()
            .anyMatch(a -> a.getAuthority().equals("MANGA_MANAGEMENT"));
    
    // Nếu chỉ có quyền TRANSLATOR_MANAGEMENT, kiểm tra quyền sở hữu
    if (hasTranslatorManagement && !hasMangaManagement && 
        !manga.getCreatedBy().equals(currentUserId)) {
        throw new AppException(ErrorCode.UNAUTHORIZED_OPERATION);
    }
    
    // Tiếp tục xử lý cập nhật như bình thường
    // ...
}
```

### Cập nhật ChapterService.java
Tương tự, cập nhật ChapterService để lưu người tạo và kiểm tra quyền sở hữu.

### Cập nhật MangaController.java
```java
@GetMapping()
@PreAuthorize("hasAnyAuthority('MANGA_MANAGEMENT', 'SYSTEM_MANAGEMENT', 'TRANSLATOR_MANAGEMENT')")
ApiResponse<Page<MangaManagementResponse>> getAllMangas(
        @RequestParam(value = "keyword", required = false) String keyword,
        @RequestParam(value = "genreName", required = false) String genreName,
        @RequestParam(value = "status", required = false) String status,
        @RequestParam(value = "yearOfRelease", required = false) Integer yearOfRelease,
        @PageableDefault(size = 10) Pageable pageable,
        @AuthenticationPrincipal Jwt jwt
) {
    // Lấy thông tin người dùng và quyền
    String userId = jwt.getSubject();
    Collection<String> authorities = jwt.getClaimAsStringList("scope");
    
    boolean hasTranslatorManagement = authorities.contains("TRANSLATOR_MANAGEMENT");
    boolean hasMangaManagement = authorities.contains("MANGA_MANAGEMENT");
    boolean hasSystemManagement = authorities.contains("SYSTEM_MANAGEMENT");
    
    // Nếu chỉ có quyền TRANSLATOR_MANAGEMENT, chỉ lấy truyện của họ
    if (hasTranslatorManagement && !hasMangaManagement && !hasSystemManagement) {
        return ApiResponse.<Page<MangaManagementResponse>>builder()
                .message("Filtered mangas retrieved successfully")
                .result(mangaService.searchAndFilterMangasByCreatedBy(keyword, genreName, status, yearOfRelease, userId, pageable))
                .build();
    }
    
    // Nếu có quyền MANGA_MANAGEMENT hoặc SYSTEM_MANAGEMENT, xem tất cả truyện
    if (keyword != null || genreName != null || status != null || yearOfRelease != null) {
        return ApiResponse.<Page<MangaManagementResponse>>builder()
                .message("Filtered mangas retrieved successfully")
                .result(mangaService.searchAndFilterActiveMangas(keyword, genreName, status, yearOfRelease, pageable))
                .build();
    }
    
    return ApiResponse.<Page<MangaManagementResponse>>builder()
            .message("Mangas retrieved successfully")
            .result(mangaService.getAllMangas(pageable))
            .build();
}
```

### Cập nhật SecurityConfig.java
```java
@Bean
public SecurityFilterChain filterChain(HttpSecurity httpSecurity) throws Exception {
    httpSecurity.authorizeHttpRequests(request ->
            request.requestMatchers(HttpMethod.GET, PUBLIC_ENDPOINTS).permitAll()
                    .requestMatchers(HttpMethod.POST, "/mangas/search/advanced").permitAll()
                    .requestMatchers(HttpMethod.POST, "/mangas").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.POST, "/chapters").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.PUT, "/mangas/{id}").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.PUT, "/chapters/{id}").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.DELETE, "/mangas/{id}").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .requestMatchers(HttpMethod.DELETE, "/chapters/{id}").hasAnyAuthority("MANGA_MANAGEMENT", "TRANSLATOR_MANAGEMENT")
                    .anyRequest()
                    .authenticated());

    // Phần còn lại giữ nguyên
}
```

### Thêm ErrorCode mới
```java
public enum ErrorCode {
    // Các mã lỗi hiện có...
    
    UNAUTHORIZED_OPERATION(403, "Bạn không có quyền thực hiện thao tác này")
    
    // Các mã lỗi khác...
}
```

## 3. CẬP NHẬT ADMIN PANEL

### Cập nhật AuthContext.tsx
```typescript
const [hasTranslatorManagement, setHasTranslatorManagement] = useState<boolean>(() => {
    const token = localStorage.getItem(TOKEN_STORAGE.ACCESS_TOKEN);
    if (token) {
        const { permissions } = parseTokenPermissions(token);
        return permissions.includes('TRANSLATOR_MANAGEMENT');
    }
    return false;
});

// Trong hàm parseTokenPermissions
const parseTokenPermissions = (token: string) => {
    try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));

        const payload = JSON.parse(jsonPayload);
        const permissions: string[] = payload.scope ? payload.scope.split(' ') : [];

        return {
            permissions,
            hasMangaManagement: permissions.includes('MANGA_MANAGEMENT'),
            hasSystemManagement: permissions.includes('SYSTEM_MANAGEMENT'),
            hasTranslatorManagement: permissions.includes('TRANSLATOR_MANAGEMENT')
        };
    } catch (error) {
        console.error("Lỗi khi parse token:", error);
        return {
            permissions: [],
            hasMangaManagement: false,
            hasSystemManagement: false,
            hasTranslatorManagement: false
        };
    }
};
```

### Cập nhật AdminLayout.tsx
```typescript
// Định nghĩa tất cả menu items
const allMenuItems = [
    {path: '/admin/dashboard', icon: faChartLine, label: 'Tổng quan', permission: 'SYSTEM_MANAGEMENT'},
    {path: '/admin/users', icon: faUsers, label: 'Quản lý người dùng', permission: 'SYSTEM_MANAGEMENT'},
    {path: '/admin/roles', icon: faUserShield, label: 'Quản lý vai trò', permission: 'SYSTEM_MANAGEMENT'},
    {path: '/admin/permissions', icon: faKey, label: 'Quản lý quyền hạn', permission: 'SYSTEM_MANAGEMENT'},
    {path: '/admin/mangas', icon: faBook, label: 'Quản lý truyện', permission: 'MANGA_MANAGEMENT'},
    {path: '/admin/chapters', icon: faBookOpen, label: 'Quản lý chương', permission: 'MANGA_MANAGEMENT'},
    {path: '/admin/genres', icon: faTags, label: 'Quản lý thể loại', permission: 'MANGA_MANAGEMENT'},
    {path: '/admin/comments', icon: faComment, label: 'Quản lý bình luận', permission: 'SYSTEM_MANAGEMENT'},
    {path: '/admin/statistics', icon: faChartBar, label: 'Thống kê', permission: 'SYSTEM_MANAGEMENT'},
    // Thêm menu cho translator
    {path: '/admin/my-mangas', icon: faBook, label: 'Truyện của tôi', permission: 'TRANSLATOR_MANAGEMENT'},
    {path: '/admin/my-chapters', icon: faBookOpen, label: 'Chương của tôi', permission: 'TRANSLATOR_MANAGEMENT'},
];

// Lọc menu items dựa trên quyền
const menuItems = useMemo(() => {
    // Nếu có quyền SYSTEM_MANAGEMENT (Super Admin), hiển thị tất cả các menu
    if (hasSystemManagement) {
        return allMenuItems;
    }

    // Nếu có quyền MANGA_MANAGEMENT, hiển thị các menu liên quan đến quản lý truyện
    if (hasMangaManagement) {
        return allMenuItems.filter(item => 
            item.permission === 'MANGA_MANAGEMENT' || 
            item.permission === 'TRANSLATOR_MANAGEMENT');
    }

    // Nếu chỉ có quyền TRANSLATOR_MANAGEMENT, chỉ hiển thị menu của translator
    if (hasTranslatorManagement) {
        return allMenuItems.filter(item => item.permission === 'TRANSLATOR_MANAGEMENT');
    }

    // Nếu không có quyền nào, không hiển thị menu nào
    return [];
}, [hasMangaManagement, hasSystemManagement, hasTranslatorManagement]);
```

### Tạo trang MyMangas.tsx
```tsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import mangaService from '../../services/manga-service';
import { MangaResponse } from '../../types/manga';
import { Pagination } from '../../components/common/Pagination';
import { SearchBar } from '../../components/common/SearchBar';
import { Button } from '../../components/ui/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faEdit, faTrash } from '@fortawesome/free-solid-svg-icons';

const MyMangas = () => {
  const navigate = useNavigate();
  const [mangas, setMangas] = useState<MangaResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch mangas created by current user
  const fetchMyMangas = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const response = await mangaService.getMyMangas(page - 1, 10, search);
      if (response) {
        setMangas(response.content);
        setTotalPages(response.totalPages);
        setCurrentPage(response.number + 1);
      }
    } catch (error) {
      console.error('Error fetching my mangas:', error);
      toast.error('Không thể tải danh sách truyện của bạn', { position: 'top-right' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMyMangas(currentPage, searchTerm);
  }, [currentPage]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
    fetchMyMangas(1, term);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCreateManga = () => {
    navigate('/admin/my-mangas/create');
  };

  const handleEditManga = (id: string) => {
    navigate(`/admin/my-mangas/edit/${id}`);
  };

  const handleDeleteManga = async (id: string) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa truyện này?')) {
      try {
        await mangaService.deleteManga(id);
        toast.success('Xóa truyện thành công', { position: 'top-right' });
        fetchMyMangas(currentPage, searchTerm);
      } catch (error) {
        console.error('Error deleting manga:', error);
        toast.error('Không thể xóa truyện', { position: 'top-right' });
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Truyện của tôi</h1>
        <Button onClick={handleCreateManga} className="bg-blue-600 hover:bg-blue-700">
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Thêm truyện mới
        </Button>
      </div>

      <SearchBar onSearch={handleSearch} placeholder="Tìm kiếm truyện..." />

      {loading ? (
        <div className="text-center py-8">Đang tải...</div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-200 rounded-lg shadow-md">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-3 px-4 text-left">Tiêu đề</th>
                  <th className="py-3 px-4 text-left">Tác giả</th>
                  <th className="py-3 px-4 text-left">Trạng thái</th>
                  <th className="py-3 px-4 text-left">Lượt xem</th>
                  <